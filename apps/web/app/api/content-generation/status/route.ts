import { NextRequest, NextResponse } from 'next/server';
import { contentGenerationStatusService } from '~/services/content-generation-status';
import { contentGenerationQueueManager } from '~/services/content-generation-queue';
import { taskDetectorService } from '~/services/task-detector';

/**
 * API endpoint to get content generation status and progress
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('job_id');
    const taskId = searchParams.get('task_id');
    const campaignId = searchParams.get('campaign_id');

    if (jobId) {
      // Get status for a specific job
      const [job, progress] = await Promise.all([
        contentGenerationStatusService.getGenerationJob(jobId),
        contentGenerationStatusService.getGenerationProgress(jobId)
      ]);

      if (!job) {
        return NextResponse.json({
          error: 'Job not found',
          job_id: jobId
        }, { status: 404 });
      }

      return NextResponse.json({
        job,
        progress,
        timestamp: new Date().toISOString()
      });

    } else if (taskId) {
      // Get status for a specific task
      const job = await contentGenerationStatusService.getGenerationJobByTaskId(taskId);

      if (!job) {
        return NextResponse.json({
          error: 'No generation job found for task',
          task_id: taskId
        }, { status: 404 });
      }

      const progress = await contentGenerationStatusService.getGenerationProgress(job.id);

      return NextResponse.json({
        job,
        progress,
        timestamp: new Date().toISOString()
      });

    } else if (campaignId) {
      // Get status for all jobs in a campaign
      const jobs = await contentGenerationStatusService.getGenerationJobsForCampaign(campaignId);

      // Get progress for each job
      const jobsWithProgress = await Promise.all(
        jobs.map(async (job) => {
          const progress = await contentGenerationStatusService.getGenerationProgress(job.id);
          return { job, progress };
        })
      );

      return NextResponse.json({
        campaign_id: campaignId,
        jobs: jobsWithProgress,
        total_jobs: jobs.length,
        timestamp: new Date().toISOString()
      });

    } else {
      // Get overall system status
      const [generationStats, queueStats] = await Promise.all([
        taskDetectorService.getGenerationStats(),
        contentGenerationQueueManager.getQueueStats()
      ]);

      return NextResponse.json({
        system_status: 'operational',
        statistics: {
          generation: generationStats,
          queue: queueStats
        },
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Error in content generation status API:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Update job status or configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.action) {
      return NextResponse.json({
        error: 'Action is required'
      }, { status: 400 });
    }

    let result: any = {};

    switch (body.action) {
      case 'cancel_job':
        if (!body.job_id) {
          return NextResponse.json({
            error: 'job_id is required for cancel_job action'
          }, { status: 400 });
        }

        try {
          await contentGenerationStatusService.updateGenerationJob(body.job_id, {
            status: 'cancelled'
          });

          // Remove from queue if it's there
          await contentGenerationStatusService.removeFromQueue(body.job_id);

          result = {
            success: true,
            message: `Job ${body.job_id} cancelled`,
            job_id: body.job_id
          };

        } catch (error) {
          console.error(`Failed to cancel job ${body.job_id}:`, error);
          return NextResponse.json({
            error: 'Failed to cancel job',
            details: error instanceof Error ? error.message : 'Unknown error',
            job_id: body.job_id
          }, { status: 500 });
        }
        break;

      case 'retry_job':
        if (!body.job_id) {
          return NextResponse.json({
            error: 'job_id is required for retry_job action'
          }, { status: 400 });
        }

        try {
          // Reset job status to queued
          await contentGenerationStatusService.updateGenerationJob(body.job_id, {
            status: 'queued',
            error: undefined
          });

          // Add back to queue with higher priority
          const priority = body.priority || 10;
          await contentGenerationStatusService.addToQueue(body.job_id, priority);

          result = {
            success: true,
            message: `Job ${body.job_id} queued for retry`,
            job_id: body.job_id,
            priority: priority
          };

        } catch (error) {
          console.error(`Failed to retry job ${body.job_id}:`, error);
          return NextResponse.json({
            error: 'Failed to retry job',
            details: error instanceof Error ? error.message : 'Unknown error',
            job_id: body.job_id
          }, { status: 500 });
        }
        break;

      case 'process_immediately':
        if (!body.job_id) {
          return NextResponse.json({
            error: 'job_id is required for process_immediately action'
          }, { status: 400 });
        }

        try {
          await contentGenerationQueueManager.processJobImmediately(body.job_id);

          result = {
            success: true,
            message: `Job ${body.job_id} processed immediately`,
            job_id: body.job_id
          };

        } catch (error) {
          console.error(`Failed to process job ${body.job_id} immediately:`, error);
          return NextResponse.json({
            error: 'Failed to process job immediately',
            details: error instanceof Error ? error.message : 'Unknown error',
            job_id: body.job_id
          }, { status: 500 });
        }
        break;

      case 'update_queue_config':
        const config = body.config;
        if (!config) {
          return NextResponse.json({
            error: 'config is required for update_queue_config action'
          }, { status: 400 });
        }

        try {
          contentGenerationQueueManager.updateConfig(config);

          result = {
            success: true,
            message: 'Queue configuration updated',
            config: config
          };

        } catch (error) {
          console.error('Failed to update queue config:', error);
          return NextResponse.json({
            error: 'Failed to update queue configuration',
            details: error instanceof Error ? error.message : 'Unknown error'
          }, { status: 500 });
        }
        break;

      default:
        return NextResponse.json({
          error: `Unknown action: ${body.action}`,
          available_actions: [
            'cancel_job',
            'retry_job',
            'process_immediately',
            'update_queue_config'
          ]
        }, { status: 400 });
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in content generation status POST API:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
