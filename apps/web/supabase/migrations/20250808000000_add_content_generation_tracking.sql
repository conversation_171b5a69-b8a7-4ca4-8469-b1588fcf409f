-- Migration: Add Content Generation Tracking Tables
-- Description: Extends database schema to support Task Generation Automation system
-- Date: 2025-08-08

-- Create content_generation_jobs table to track orchestration jobs
CREATE TABLE "public"."content_generation_jobs" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "task_id" text NOT NULL, -- References company_content.task_id
    "company_id" uuid NOT NULL,
    "campaign_id" uuid NOT NULL,
    "content_id" uuid, -- References company_content.id when available
    "priority" text NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    "status" text NOT NULL DEFAULT 'queued' CHECK (status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),
    "created_at" timestamp with time zone DEFAULT now(),
    "started_at" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "retry_count" integer DEFAULT 0,
    "max_retries" integer DEFAULT 3,
    "error_message" text,
    "error_details" jsonb,
    "result_data" jsonb, -- Stores the complete ContentPackage result
    "processing_time_ms" integer,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

-- Create content_generation_steps table to track individual API call progress
CREATE TABLE "public"."content_generation_steps" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "job_id" uuid NOT NULL,
    "step_name" text NOT NULL CHECK (step_name IN ('content', 'visual_description', 'image', 'assembly')),
    "status" text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'skipped')),
    "started_at" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "retry_count" integer DEFAULT 0,
    "error_message" text,
    "error_details" jsonb,
    "request_data" jsonb, -- API request payload
    "response_data" jsonb, -- API response data
    "processing_time_ms" integer,
    "created_at" timestamp with time zone DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now()
);

-- Create content_generation_queue table for queue management
CREATE TABLE "public"."content_generation_queue" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "job_id" uuid NOT NULL,
    "priority" integer DEFAULT 0, -- Higher number = higher priority
    "scheduled_at" timestamp with time zone DEFAULT now(),
    "attempts" integer DEFAULT 0,
    "max_attempts" integer DEFAULT 3,
    "next_retry_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now()
);

-- Add indexes for performance
CREATE UNIQUE INDEX content_generation_jobs_pkey ON public.content_generation_jobs USING btree (id);
CREATE INDEX idx_content_generation_jobs_task_id ON public.content_generation_jobs USING btree (task_id);
CREATE INDEX idx_content_generation_jobs_status ON public.content_generation_jobs USING btree (status);
CREATE INDEX idx_content_generation_jobs_company_campaign ON public.content_generation_jobs USING btree (company_id, campaign_id);
CREATE INDEX idx_content_generation_jobs_created_at ON public.content_generation_jobs USING btree (created_at);

CREATE UNIQUE INDEX content_generation_steps_pkey ON public.content_generation_steps USING btree (id);
CREATE INDEX idx_content_generation_steps_job_id ON public.content_generation_steps USING btree (job_id);
CREATE INDEX idx_content_generation_steps_status ON public.content_generation_steps USING btree (status);
CREATE UNIQUE INDEX idx_content_generation_steps_job_step ON public.content_generation_steps USING btree (job_id, step_name);

CREATE UNIQUE INDEX content_generation_queue_pkey ON public.content_generation_queue USING btree (id);
CREATE UNIQUE INDEX idx_content_generation_queue_job_id ON public.content_generation_queue USING btree (job_id);
CREATE INDEX idx_content_generation_queue_priority ON public.content_generation_queue USING btree (priority DESC, scheduled_at ASC);
CREATE INDEX idx_content_generation_queue_retry ON public.content_generation_queue USING btree (next_retry_at) WHERE next_retry_at IS NOT NULL;

-- Add primary key constraints
ALTER TABLE "public"."content_generation_jobs" ADD CONSTRAINT "content_generation_jobs_pkey" PRIMARY KEY USING INDEX "content_generation_jobs_pkey";
ALTER TABLE "public"."content_generation_steps" ADD CONSTRAINT "content_generation_steps_pkey" PRIMARY KEY USING INDEX "content_generation_steps_pkey";
ALTER TABLE "public"."content_generation_queue" ADD CONSTRAINT "content_generation_queue_pkey" PRIMARY KEY USING INDEX "content_generation_queue_pkey";

-- Add foreign key constraints
ALTER TABLE "public"."content_generation_jobs" ADD CONSTRAINT "content_generation_jobs_company_id_fkey" 
    FOREIGN KEY (company_id) REFERENCES accounts(id) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."content_generation_jobs" ADD CONSTRAINT "content_generation_jobs_campaign_id_fkey" 
    FOREIGN KEY (campaign_id) REFERENCES company_campaigns(id) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."content_generation_jobs" ADD CONSTRAINT "content_generation_jobs_content_id_fkey" 
    FOREIGN KEY (content_id) REFERENCES company_content(id) ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."content_generation_steps" ADD CONSTRAINT "content_generation_steps_job_id_fkey" 
    FOREIGN KEY (job_id) REFERENCES content_generation_jobs(id) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."content_generation_queue" ADD CONSTRAINT "content_generation_queue_job_id_fkey" 
    FOREIGN KEY (job_id) REFERENCES content_generation_jobs(id) ON UPDATE CASCADE ON DELETE CASCADE;

-- Add RLS policies for content_generation_jobs
CREATE POLICY "team_members_can_select_content_generation_jobs" ON public.content_generation_jobs
FOR SELECT USING (
    public.has_role_on_account(company_id)
);

CREATE POLICY "team_members_can_insert_content_generation_jobs" ON public.content_generation_jobs
FOR INSERT WITH CHECK (
    public.has_role_on_account(company_id)
);

CREATE POLICY "team_members_can_update_content_generation_jobs" ON public.content_generation_jobs
FOR UPDATE USING (
    public.has_role_on_account(company_id)
) WITH CHECK (
    public.has_role_on_account(company_id)
);

-- Add RLS policies for content_generation_steps
CREATE POLICY "team_members_can_select_content_generation_steps" ON public.content_generation_steps
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM content_generation_jobs 
        WHERE id = content_generation_steps.job_id 
        AND public.has_role_on_account(company_id)
    )
);

CREATE POLICY "team_members_can_insert_content_generation_steps" ON public.content_generation_steps
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM content_generation_jobs 
        WHERE id = content_generation_steps.job_id 
        AND public.has_role_on_account(company_id)
    )
);

CREATE POLICY "team_members_can_update_content_generation_steps" ON public.content_generation_steps
FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM content_generation_jobs 
        WHERE id = content_generation_steps.job_id 
        AND public.has_role_on_account(company_id)
    )
) WITH CHECK (
    EXISTS (
        SELECT 1 FROM content_generation_jobs 
        WHERE id = content_generation_steps.job_id 
        AND public.has_role_on_account(company_id)
    )
);

-- Add RLS policies for content_generation_queue (service role only)
CREATE POLICY "service_role_can_manage_content_generation_queue" ON public.content_generation_queue
FOR ALL USING (auth.role() = 'service_role');

-- Enable RLS on all tables
ALTER TABLE "public"."content_generation_jobs" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."content_generation_steps" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."content_generation_queue" ENABLE ROW LEVEL SECURITY;

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON TABLE "public"."content_generation_jobs" TO "authenticated";
GRANT SELECT, INSERT, UPDATE ON TABLE "public"."content_generation_steps" TO "authenticated";

-- Grant full permissions to service role
GRANT ALL ON TABLE "public"."content_generation_jobs" TO "service_role";
GRANT ALL ON TABLE "public"."content_generation_steps" TO "service_role";
GRANT ALL ON TABLE "public"."content_generation_queue" TO "service_role";

-- Add updated_at trigger for content_generation_steps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_content_generation_steps_updated_at 
    BEFORE UPDATE ON content_generation_steps 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add fields to existing company_content table for generation tracking
ALTER TABLE "public"."company_content" 
ADD COLUMN IF NOT EXISTS "generation_job_id" uuid,
ADD COLUMN IF NOT EXISTS "generation_status" text DEFAULT 'manual' CHECK (generation_status IN ('manual', 'queued', 'generating', 'completed', 'failed')),
ADD COLUMN IF NOT EXISTS "generation_started_at" timestamp with time zone,
ADD COLUMN IF NOT EXISTS "generation_completed_at" timestamp with time zone,
ADD COLUMN IF NOT EXISTS "generation_error" text;

-- Add foreign key constraint for generation_job_id
ALTER TABLE "public"."company_content" 
ADD CONSTRAINT "company_content_generation_job_id_fkey" 
FOREIGN KEY (generation_job_id) REFERENCES content_generation_jobs(id) ON UPDATE CASCADE ON DELETE SET NULL;

-- Add index for generation tracking
CREATE INDEX IF NOT EXISTS idx_company_content_generation_status ON public.company_content USING btree (generation_status);
CREATE INDEX IF NOT EXISTS idx_company_content_generation_job ON public.company_content USING btree (generation_job_id);

-- Create function to automatically create generation job when content is created
CREATE OR REPLACE FUNCTION create_generation_job_for_content()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create generation job for new content that doesn't already have one
    IF NEW.generation_job_id IS NULL AND NEW.generation_status = 'manual' THEN
        -- Check if this content should be auto-generated (has basic task info but no detailed content)
        IF NEW.task_id IS NOT NULL AND NEW.task_title IS NOT NULL AND (NEW.content IS NULL OR NEW.content = '') THEN
            -- Update the content to indicate it should be queued for generation
            NEW.generation_status = 'queued';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically queue content for generation
CREATE TRIGGER trigger_create_generation_job_for_content
    BEFORE INSERT ON company_content
    FOR EACH ROW
    EXECUTE FUNCTION create_generation_job_for_content();

-- Create function to get generation progress for a job
CREATE OR REPLACE FUNCTION get_generation_progress(job_id_param uuid)
RETURNS jsonb AS $$
DECLARE
    job_record content_generation_jobs%ROWTYPE;
    steps_data jsonb;
    progress_data jsonb;
BEGIN
    -- Get job record
    SELECT * INTO job_record FROM content_generation_jobs WHERE id = job_id_param;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('error', 'Job not found');
    END IF;
    
    -- Get steps progress
    SELECT jsonb_agg(
        jsonb_build_object(
            'step_name', step_name,
            'status', status,
            'started_at', started_at,
            'completed_at', completed_at,
            'processing_time_ms', processing_time_ms,
            'error_message', error_message
        ) ORDER BY 
            CASE step_name 
                WHEN 'content' THEN 1
                WHEN 'visual_description' THEN 2
                WHEN 'image' THEN 3
                WHEN 'assembly' THEN 4
            END
    ) INTO steps_data
    FROM content_generation_steps 
    WHERE job_id = job_id_param;
    
    -- Build progress response
    SELECT jsonb_build_object(
        'job_id', job_record.id,
        'task_id', job_record.task_id,
        'status', job_record.status,
        'priority', job_record.priority,
        'created_at', job_record.created_at,
        'started_at', job_record.started_at,
        'completed_at', job_record.completed_at,
        'retry_count', job_record.retry_count,
        'max_retries', job_record.max_retries,
        'processing_time_ms', job_record.processing_time_ms,
        'error_message', job_record.error_message,
        'steps', COALESCE(steps_data, '[]'::jsonb),
        'progress_percentage', 
            CASE 
                WHEN job_record.status = 'completed' THEN 100
                WHEN job_record.status = 'failed' THEN 0
                ELSE COALESCE(
                    (SELECT COUNT(*) * 25 FROM content_generation_steps 
                     WHERE job_id = job_id_param AND status = 'completed'), 0
                )
            END
    ) INTO progress_data;
    
    RETURN progress_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
