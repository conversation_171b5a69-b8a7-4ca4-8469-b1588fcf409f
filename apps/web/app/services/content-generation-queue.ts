'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { contentGenerationOrchestrator } from './content-generation-orchestrator';
import { contentGenerationStatusService } from './content-generation-status';
import { GenerationJob } from '~/types/push-server.types';

/**
 * Content Generation Queue Manager
 * Handles concurrent processing of generation jobs with rate limiting and priority management
 */
export class ContentGenerationQueueManager {
  private isProcessing: boolean = false;
  private processingInterval: NodeJS.Timeout | null = null;
  private maxConcurrentJobs: number = 3; // Adjust based on Push Server capacity
  private processingIntervalMs: number = 5000; // Check queue every 5 seconds
  private rateLimitConfig = {
    maxRequestsPerMinute: 30, // Adjust based on Push Server limits
    maxRequestsPerHour: 1000,
    requestCounts: {
      minute: 0,
      hour: 0,
      lastMinuteReset: Date.now(),
      lastHourReset: Date.now()
    }
  };

  /**
   * Start the queue processor
   */
  async startProcessing(): Promise<void> {
    if (this.isProcessing) {
      console.log('Queue processor is already running');
      return;
    }

    this.isProcessing = true;
    console.log('Starting content generation queue processor');

    // Start the processing interval
    this.processingInterval = setInterval(async () => {
      try {
        await this.processQueue();
      } catch (error) {
        console.error('Error in queue processing interval:', error);
      }
    }, this.processingIntervalMs);

    // Process immediately
    await this.processQueue();
  }

  /**
   * Stop the queue processor
   */
  stopProcessing(): void {
    if (!this.isProcessing) {
      return;
    }

    this.isProcessing = false;
    
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    console.log('Stopped content generation queue processor');
  }

  /**
   * Process the queue - main processing loop
   */
  private async processQueue(): Promise<void> {
    if (!this.isProcessing) {
      return;
    }

    try {
      // Update rate limit counters
      this.updateRateLimitCounters();

      // Check if we're rate limited
      if (this.isRateLimited()) {
        console.log('Rate limited, skipping queue processing');
        return;
      }

      // Get currently processing jobs count
      const processingJobs = await this.getProcessingJobsCount();
      
      if (processingJobs >= this.maxConcurrentJobs) {
        console.log(`Max concurrent jobs reached (${processingJobs}/${this.maxConcurrentJobs}), waiting...`);
        return;
      }

      // Get available slots
      const availableSlots = this.maxConcurrentJobs - processingJobs;
      
      // Get jobs from queue
      const queuedJobs = await this.getJobsFromQueue(availableSlots);
      
      if (queuedJobs.length === 0) {
        return; // No jobs to process
      }

      console.log(`Processing ${queuedJobs.length} jobs from queue (${availableSlots} slots available)`);

      // Process jobs concurrently
      const processingPromises = queuedJobs.map(job => this.processJob(job));
      
      // Don't await all - let them run in background
      Promise.allSettled(processingPromises).then(results => {
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        console.log(`Batch processing completed: ${successful} successful, ${failed} failed`);
      });

    } catch (error) {
      console.error('Error in processQueue:', error);
    }
  }

  /**
   * Get jobs from the queue based on priority
   */
  private async getJobsFromQueue(limit: number): Promise<GenerationJob[]> {
    const server = getSupabaseServerClient();

    try {
      // Get jobs from the queue table ordered by priority and scheduled time
      const { data: queueItems, error } = await server
        .from('content_generation_queue')
        .select(`
          job_id,
          priority,
          scheduled_at,
          attempts,
          max_attempts,
          next_retry_at
        `)
        .or(`next_retry_at.is.null,next_retry_at.lte.${new Date().toISOString()}`)
        .lt('attempts', 'max_attempts')
        .order('priority', { ascending: false })
        .order('scheduled_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error getting jobs from queue:', error);
        return [];
      }

      if (!queueItems || queueItems.length === 0) {
        return [];
      }

      // Get the actual job details
      const jobIds = queueItems.map(item => item.job_id);
      const jobs = await Promise.all(
        jobIds.map(async (jobId) => {
          const job = await contentGenerationStatusService.getGenerationJob(jobId);
          return job;
        })
      );

      // Filter out null jobs and return valid ones
      return jobs.filter(job => job !== null) as GenerationJob[];

    } catch (error) {
      console.error('Error in getJobsFromQueue:', error);
      return [];
    }
  }

  /**
   * Process a single job
   */
  private async processJob(job: GenerationJob): Promise<void> {
    const server = getSupabaseServerClient();

    try {
      console.log(`Starting processing of job ${job.id}`);

      // Update queue item attempts - we'll increment this in the failure handler
      // For now, just update the timestamp
      await server
        .from('content_generation_queue')
        .update({
          updated_at: new Date().toISOString()
        })
        .eq('job_id', job.id);

      // Update rate limit counters
      this.incrementRateLimitCounters();

      // Process the job through the orchestrator
      await contentGenerationOrchestrator.processGenerationJob(job.id);

      // Remove job from queue on success
      await contentGenerationStatusService.removeFromQueue(job.id);

      console.log(`Successfully completed job ${job.id}`);

    } catch (error) {
      console.error(`Error processing job ${job.id}:`, error);

      // Handle job failure
      await this.handleJobFailure(job, error);
    }
  }

  /**
   * Handle job failure with retry logic
   */
  private async handleJobFailure(job: GenerationJob, error: any): Promise<void> {
    const server = getSupabaseServerClient();

    try {
      // Get current queue item
      const { data: queueItem, error: queueError } = await server
        .from('content_generation_queue')
        .select('*')
        .eq('job_id', job.id)
        .single();

      if (queueError || !queueItem) {
        console.error(`Could not find queue item for job ${job.id}`);
        return;
      }

      const currentAttempts = queueItem.attempts + 1;
      const maxAttempts = queueItem.max_attempts;

      if (currentAttempts >= maxAttempts) {
        // Max retries reached, mark job as failed and remove from queue
        console.log(`Job ${job.id} failed after ${currentAttempts} attempts, marking as failed`);
        
        await contentGenerationStatusService.updateGenerationJob(job.id, {
          status: 'failed',
          error: {
            code: 'MAX_RETRIES_EXCEEDED',
            message: `Job failed after ${currentAttempts} attempts: ${error.message}`,
            details: error
          }
        });

        await contentGenerationStatusService.removeFromQueue(job.id);

      } else {
        // Schedule retry with exponential backoff
        const retryDelayMs = Math.min(
          1000 * Math.pow(2, currentAttempts), // Exponential backoff
          300000 // Max 5 minutes
        );
        const nextRetryAt = new Date(Date.now() + retryDelayMs).toISOString();

        console.log(`Job ${job.id} failed (attempt ${currentAttempts}/${maxAttempts}), retrying in ${retryDelayMs}ms`);

        await server
          .from('content_generation_queue')
          .update({
            attempts: currentAttempts,
            next_retry_at: nextRetryAt,
            updated_at: new Date().toISOString()
          })
          .eq('job_id', job.id);

        // Update job status to queued for retry
        await contentGenerationStatusService.updateGenerationJob(job.id, {
          status: 'queued'
        });
      }

    } catch (retryError) {
      console.error(`Error handling job failure for ${job.id}:`, retryError);
    }
  }

  /**
   * Get count of currently processing jobs
   */
  private async getProcessingJobsCount(): Promise<number> {
    const server = getSupabaseServerClient();

    try {
      const { count, error } = await server
        .from('content_generation_jobs')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'processing');

      if (error) {
        console.error('Error getting processing jobs count:', error);
        return 0;
      }

      return count || 0;

    } catch (error) {
      console.error('Error in getProcessingJobsCount:', error);
      return 0;
    }
  }

  /**
   * Update rate limit counters
   */
  private updateRateLimitCounters(): void {
    const now = Date.now();
    const { requestCounts } = this.rateLimitConfig;

    // Reset minute counter if needed
    if (now - requestCounts.lastMinuteReset >= 60000) {
      requestCounts.minute = 0;
      requestCounts.lastMinuteReset = now;
    }

    // Reset hour counter if needed
    if (now - requestCounts.lastHourReset >= 3600000) {
      requestCounts.hour = 0;
      requestCounts.lastHourReset = now;
    }
  }

  /**
   * Check if we're currently rate limited
   */
  private isRateLimited(): boolean {
    const { requestCounts, maxRequestsPerMinute, maxRequestsPerHour } = this.rateLimitConfig;

    return (
      requestCounts.minute >= maxRequestsPerMinute ||
      requestCounts.hour >= maxRequestsPerHour
    );
  }

  /**
   * Increment rate limit counters
   */
  private incrementRateLimitCounters(): void {
    this.rateLimitConfig.requestCounts.minute++;
    this.rateLimitConfig.requestCounts.hour++;
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    queued: number;
    processing: number;
    failed_retries: number;
    rate_limit_status: {
      requests_this_minute: number;
      requests_this_hour: number;
      is_rate_limited: boolean;
    };
  }> {
    const server = getSupabaseServerClient();

    try {
      // Get queue counts
      const [queuedResult, processingResult, failedRetriesResult] = await Promise.all([
        server
          .from('content_generation_queue')
          .select('*', { count: 'exact', head: true })
          .lt('attempts', 'max_attempts')
          .or(`next_retry_at.is.null,next_retry_at.lte.${new Date().toISOString()}`),
        
        server
          .from('content_generation_jobs')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'processing'),
        
        server
          .from('content_generation_queue')
          .select('*', { count: 'exact', head: true })
          .not('next_retry_at', 'is', null)
          .gt('next_retry_at', new Date().toISOString())
      ]);

      this.updateRateLimitCounters();

      return {
        queued: queuedResult.count || 0,
        processing: processingResult.count || 0,
        failed_retries: failedRetriesResult.count || 0,
        rate_limit_status: {
          requests_this_minute: this.rateLimitConfig.requestCounts.minute,
          requests_this_hour: this.rateLimitConfig.requestCounts.hour,
          is_rate_limited: this.isRateLimited()
        }
      };

    } catch (error) {
      console.error('Error getting queue stats:', error);
      return {
        queued: 0,
        processing: 0,
        failed_retries: 0,
        rate_limit_status: {
          requests_this_minute: 0,
          requests_this_hour: 0,
          is_rate_limited: false
        }
      };
    }
  }

  /**
   * Manually process a specific job (bypass queue)
   */
  async processJobImmediately(jobId: string): Promise<void> {
    try {
      console.log(`Processing job ${jobId} immediately`);

      // Check rate limits
      if (this.isRateLimited()) {
        throw new Error('Rate limited - cannot process job immediately');
      }

      // Get the job
      const job = await contentGenerationStatusService.getGenerationJob(jobId);
      if (!job) {
        throw new Error(`Job ${jobId} not found`);
      }

      // Update rate limit counters
      this.incrementRateLimitCounters();

      // Process the job
      await contentGenerationOrchestrator.processGenerationJob(jobId);

      // Remove from queue if it was queued
      await contentGenerationStatusService.removeFromQueue(jobId);

      console.log(`Successfully processed job ${jobId} immediately`);

    } catch (error) {
      console.error(`Error processing job ${jobId} immediately:`, error);
      throw error;
    }
  }

  /**
   * Clear all jobs from the queue (emergency function)
   */
  async clearQueue(): Promise<number> {
    const server = getSupabaseServerClient();

    try {
      const { data: queueItems, error: selectError } = await server
        .from('content_generation_queue')
        .select('job_id');

      if (selectError) {
        throw selectError;
      }

      const { error: deleteError } = await server
        .from('content_generation_queue')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

      if (deleteError) {
        throw deleteError;
      }

      const clearedCount = queueItems?.length || 0;
      console.log(`Cleared ${clearedCount} jobs from queue`);
      return clearedCount;

    } catch (error) {
      console.error('Error clearing queue:', error);
      throw error;
    }
  }

  /**
   * Update queue configuration
   */
  updateConfig(config: {
    maxConcurrentJobs?: number;
    processingIntervalMs?: number;
    maxRequestsPerMinute?: number;
    maxRequestsPerHour?: number;
  }): void {
    if (config.maxConcurrentJobs !== undefined) {
      this.maxConcurrentJobs = config.maxConcurrentJobs;
    }
    if (config.processingIntervalMs !== undefined) {
      this.processingIntervalMs = config.processingIntervalMs;
      
      // Restart processing with new interval if currently running
      if (this.isProcessing) {
        this.stopProcessing();
        this.startProcessing();
      }
    }
    if (config.maxRequestsPerMinute !== undefined) {
      this.rateLimitConfig.maxRequestsPerMinute = config.maxRequestsPerMinute;
    }
    if (config.maxRequestsPerHour !== undefined) {
      this.rateLimitConfig.maxRequestsPerHour = config.maxRequestsPerHour;
    }

    console.log('Updated queue configuration:', {
      maxConcurrentJobs: this.maxConcurrentJobs,
      processingIntervalMs: this.processingIntervalMs,
      maxRequestsPerMinute: this.rateLimitConfig.maxRequestsPerMinute,
      maxRequestsPerHour: this.rateLimitConfig.maxRequestsPerHour
    });
  }
}

// Export singleton instance
export const contentGenerationQueueManager = new ContentGenerationQueueManager();
