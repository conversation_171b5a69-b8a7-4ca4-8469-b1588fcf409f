/**
 * TypeScript interfaces for Push Server API integration
 * Used by the Task Generation Automation system
 */

// Base API response structure
export interface PushServerResponse<T = any> {
  result?: T;
  error?: string;
  status?: 'success' | 'error';
}

// Content Generation API
export interface GenerateContentRequest {
  task_id: string;
  task_title: string;
  task_description: string;
  content_type: string;
  channel: string;
  brand_brief: string;
  campaign_brief?: string;
  language: string;
  company_id: string;
  campaign_id: string;
}

export interface GenerateContentResponse {
  content: {
    title: string;
    body: string;
    description: string;
    hashtags?: string[];
    call_to_action?: string;
  };
  metadata: {
    word_count: number;
    character_count: number;
    estimated_reading_time: number;
  };
}

// Visual Description Generation API
export interface GenerateVisualDescriptionRequest {
  brand_brief: string;
  content: string;
  initial_visual_desc?: string;
  image_gen_styles: string;
  task_id?: string;
  content_type?: string;
}

export interface GenerateVisualDescriptionResponse {
  visual_description: string;
  style_elements: {
    color_palette: string[];
    mood: string;
    composition: string;
    lighting: string;
  };
  technical_specs: {
    aspect_ratio: string;
    resolution: string;
    format: string;
  };
}

// Image Generation API
export interface GenerateImageRequest {
  image_prompt: string;
  aspect_ratio: string;
  prompt_upsampling?: string;
  language?: string;
  style_preset?: string;
  quality?: 'standard' | 'hd';
}

export interface GenerateImageResponse {
  image_url: string;
  image_path: string;
  metadata: {
    width: number;
    height: number;
    format: string;
    size_bytes: number;
  };
}

// Content Package Assembly
export interface ContentPackage {
  task_id: string;
  content: GenerateContentResponse;
  visual_description: GenerateVisualDescriptionResponse;
  generated_image?: GenerateImageResponse;
  status: 'complete' | 'partial' | 'failed';
  created_at: string;
  processing_time_ms: number;
}

// API Error types
export interface PushServerError {
  code: string;
  message: string;
  details?: any;
  retry_after?: number;
}

// Rate limiting information
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset_time: number;
  retry_after?: number;
}

// API call configuration
export interface ApiCallConfig {
  timeout_ms: number;
  max_retries: number;
  retry_delay_ms: number;
  exponential_backoff: boolean;
}

// Orchestration types
export type GenerationStep = 'content' | 'visual_description' | 'image' | 'assembly';

export interface GenerationProgress {
  task_id: string;
  current_step: GenerationStep;
  completed_steps: GenerationStep[];
  failed_steps: GenerationStep[];
  total_steps: number;
  progress_percentage: number;
  estimated_completion_time?: string;
  error?: PushServerError;
}

export interface GenerationJob {
  id: string;
  task_id: string;
  company_id: string;
  campaign_id: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  progress: GenerationProgress;
  result?: ContentPackage;
  error?: PushServerError;
  retry_count: number;
  max_retries: number;
}
