import { NextRequest, NextResponse } from 'next/server';
import { taskDetectorService } from '~/services/task-detector';

/**
 * Webhook handler for task creation events
 * Automatically triggers content generation when new tasks are created
 */
export async function POST(request: NextRequest) {
  try {
    // Verify webhook authenticity (you may want to add signature verification)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the webhook payload
    const payload = await request.json();
    
    // Validate payload structure
    if (!payload.type || !payload.record) {
      return NextResponse.json({ error: 'Invalid payload' }, { status: 400 });
    }

    console.log('Received task creation webhook:', payload.type);

    let triggeredJobs: string[] = [];

    switch (payload.type) {
      case 'INSERT':
        // Handle new task creation
        if (payload.table === 'company_content') {
          const newTask = payload.record;
          
          // Check if this task should trigger generation
          if (newTask.generation_status === 'queued') {
            console.log(`Processing new task ${newTask.id} for content generation`);
            
            try {
              const jobId = await taskDetectorService.triggerGenerationForTask(newTask);
              if (jobId) {
                triggeredJobs.push(jobId);
                console.log(`Triggered generation job ${jobId} for new task ${newTask.id}`);
              }
            } catch (error) {
              console.error(`Failed to trigger generation for new task ${newTask.id}:`, error);
              return NextResponse.json({ 
                error: 'Failed to trigger content generation',
                details: error instanceof Error ? error.message : 'Unknown error'
              }, { status: 500 });
            }
          }
        }
        break;

      case 'UPDATE':
        // Handle task updates that might trigger generation
        if (payload.table === 'company_content') {
          const updatedTask = payload.record;
          const oldTask = payload.old_record;
          
          // Check if generation status changed to 'queued'
          if (oldTask?.generation_status !== 'queued' && updatedTask.generation_status === 'queued') {
            console.log(`Task ${updatedTask.id} status changed to queued, triggering generation`);
            
            try {
              const jobId = await taskDetectorService.triggerGenerationForTask(updatedTask);
              if (jobId) {
                triggeredJobs.push(jobId);
                console.log(`Triggered generation job ${jobId} for updated task ${updatedTask.id}`);
              }
            } catch (error) {
              console.error(`Failed to trigger generation for updated task ${updatedTask.id}:`, error);
              return NextResponse.json({ 
                error: 'Failed to trigger content generation',
                details: error instanceof Error ? error.message : 'Unknown error'
              }, { status: 500 });
            }
          }
        }
        break;

      case 'BATCH_SCAN':
        // Handle batch scanning request
        console.log('Processing batch scan request for tasks needing generation');
        try {
          triggeredJobs = await taskDetectorService.scanForTasksNeedingGeneration();
          console.log(`Batch scan triggered ${triggeredJobs.length} generation jobs`);
        } catch (error) {
          console.error('Failed to process batch scan:', error);
          return NextResponse.json({ 
            error: 'Failed to process batch scan',
            details: error instanceof Error ? error.message : 'Unknown error'
          }, { status: 500 });
        }
        break;

      case 'CLEANUP_STALE':
        // Handle cleanup of stale jobs
        console.log('Processing cleanup request for stale generation jobs');
        try {
          const cleanedCount = await taskDetectorService.cleanupStaleJobs();
          console.log(`Cleaned up ${cleanedCount} stale generation jobs`);
          return NextResponse.json({ 
            success: true, 
            message: `Cleaned up ${cleanedCount} stale jobs`,
            cleaned_count: cleanedCount
          });
        } catch (error) {
          console.error('Failed to cleanup stale jobs:', error);
          return NextResponse.json({ 
            error: 'Failed to cleanup stale jobs',
            details: error instanceof Error ? error.message : 'Unknown error'
          }, { status: 500 });
        }

      default:
        console.log(`Ignoring webhook event type: ${payload.type}`);
        return NextResponse.json({ success: true, message: 'Event ignored' });
    }

    // Return success response
    return NextResponse.json({ 
      success: true, 
      message: `Processed ${payload.type} event`,
      triggered_jobs: triggeredJobs,
      job_count: triggeredJobs.length
    });

  } catch (error) {
    console.error('Error processing task creation webhook:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Health check endpoint for the webhook
 */
export async function GET() {
  try {
    // Get current generation statistics
    const stats = await taskDetectorService.getGenerationStats();
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      generation_stats: stats
    });
  } catch (error) {
    console.error('Error in webhook health check:', error);
    return NextResponse.json({
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
