import { NextRequest, NextResponse } from 'next/server';
import { taskDetectorService } from '~/services/task-detector';
import { contentGenerationQueueManager } from '~/services/content-generation-queue';

/**
 * API endpoint to manually trigger content generation for tasks
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    if (!body.task_id && !body.action) {
      return NextResponse.json({
        error: 'Either task_id or action is required'
      }, { status: 400 });
    }

    let result: any = {};

    if (body.task_id) {
      // Trigger generation for a specific task
      const { task_id, priority = 'normal' } = body;
      
      console.log(`Manual trigger requested for task ${task_id} with priority ${priority}`);
      
      try {
        const jobId = await taskDetectorService.manuallyTriggerTask(task_id);
        
        result = {
          success: true,
          message: `Content generation triggered for task ${task_id}`,
          job_id: jobId,
          task_id: task_id,
          priority: priority
        };
        
        console.log(`Successfully triggered generation job ${jobId} for task ${task_id}`);
        
      } catch (error) {
        console.error(`Failed to trigger generation for task ${task_id}:`, error);
        return NextResponse.json({
          error: 'Failed to trigger content generation',
          details: error instanceof Error ? error.message : 'Unknown error',
          task_id: task_id
        }, { status: 500 });
      }
      
    } else if (body.action) {
      // Handle batch actions
      switch (body.action) {
        case 'scan_all':
          console.log('Manual batch scan requested');
          try {
            const triggeredJobs = await taskDetectorService.scanForTasksNeedingGeneration();
            result = {
              success: true,
              message: `Batch scan completed`,
              triggered_jobs: triggeredJobs,
              job_count: triggeredJobs.length
            };
            console.log(`Batch scan triggered ${triggeredJobs.length} jobs`);
          } catch (error) {
            console.error('Failed to perform batch scan:', error);
            return NextResponse.json({
              error: 'Failed to perform batch scan',
              details: error instanceof Error ? error.message : 'Unknown error'
            }, { status: 500 });
          }
          break;
          
        case 'process_recent':
          const minutesBack = body.minutes_back || 5;
          console.log(`Processing recent tasks from last ${minutesBack} minutes`);
          try {
            const triggeredJobs = await taskDetectorService.processRecentTasks(minutesBack);
            result = {
              success: true,
              message: `Processed recent tasks from last ${minutesBack} minutes`,
              triggered_jobs: triggeredJobs,
              job_count: triggeredJobs.length
            };
            console.log(`Recent tasks processing triggered ${triggeredJobs.length} jobs`);
          } catch (error) {
            console.error('Failed to process recent tasks:', error);
            return NextResponse.json({
              error: 'Failed to process recent tasks',
              details: error instanceof Error ? error.message : 'Unknown error'
            }, { status: 500 });
          }
          break;
          
        case 'cleanup_stale':
          const hoursOld = body.hours_old || 2;
          console.log(`Cleaning up stale jobs older than ${hoursOld} hours`);
          try {
            const cleanedCount = await taskDetectorService.cleanupStaleJobs(hoursOld);
            result = {
              success: true,
              message: `Cleaned up ${cleanedCount} stale jobs`,
              cleaned_count: cleanedCount
            };
            console.log(`Cleaned up ${cleanedCount} stale jobs`);
          } catch (error) {
            console.error('Failed to cleanup stale jobs:', error);
            return NextResponse.json({
              error: 'Failed to cleanup stale jobs',
              details: error instanceof Error ? error.message : 'Unknown error'
            }, { status: 500 });
          }
          break;
          
        case 'start_queue':
          console.log('Starting queue processor');
          try {
            await contentGenerationQueueManager.startProcessing();
            result = {
              success: true,
              message: 'Queue processor started'
            };
          } catch (error) {
            console.error('Failed to start queue processor:', error);
            return NextResponse.json({
              error: 'Failed to start queue processor',
              details: error instanceof Error ? error.message : 'Unknown error'
            }, { status: 500 });
          }
          break;
          
        case 'stop_queue':
          console.log('Stopping queue processor');
          try {
            contentGenerationQueueManager.stopProcessing();
            result = {
              success: true,
              message: 'Queue processor stopped'
            };
          } catch (error) {
            console.error('Failed to stop queue processor:', error);
            return NextResponse.json({
              error: 'Failed to stop queue processor',
              details: error instanceof Error ? error.message : 'Unknown error'
            }, { status: 500 });
          }
          break;
          
        case 'clear_queue':
          console.log('Clearing all jobs from queue');
          try {
            const clearedCount = await contentGenerationQueueManager.clearQueue();
            result = {
              success: true,
              message: `Cleared ${clearedCount} jobs from queue`,
              cleared_count: clearedCount
            };
          } catch (error) {
            console.error('Failed to clear queue:', error);
            return NextResponse.json({
              error: 'Failed to clear queue',
              details: error instanceof Error ? error.message : 'Unknown error'
            }, { status: 500 });
          }
          break;
          
        default:
          return NextResponse.json({
            error: `Unknown action: ${body.action}`,
            available_actions: [
              'scan_all',
              'process_recent',
              'cleanup_stale',
              'start_queue',
              'stop_queue',
              'clear_queue'
            ]
          }, { status: 400 });
      }
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in content generation trigger API:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Get available trigger options and current status
 */
export async function GET() {
  try {
    // Get current statistics
    const [generationStats, queueStats] = await Promise.all([
      taskDetectorService.getGenerationStats(),
      contentGenerationQueueManager.getQueueStats()
    ]);

    return NextResponse.json({
      status: 'available',
      timestamp: new Date().toISOString(),
      statistics: {
        generation: generationStats,
        queue: queueStats
      },
      available_actions: {
        single_task: {
          endpoint: 'POST /api/content-generation/trigger',
          body: {
            task_id: 'string (required)',
            priority: 'string (optional: low|normal|high|urgent)'
          },
          description: 'Trigger content generation for a specific task'
        },
        batch_actions: {
          scan_all: {
            body: { action: 'scan_all' },
            description: 'Scan all tasks and trigger generation for those needing it'
          },
          process_recent: {
            body: { action: 'process_recent', minutes_back: 5 },
            description: 'Process tasks created in the last N minutes'
          },
          cleanup_stale: {
            body: { action: 'cleanup_stale', hours_old: 2 },
            description: 'Clean up stale generation jobs older than N hours'
          },
          start_queue: {
            body: { action: 'start_queue' },
            description: 'Start the queue processor'
          },
          stop_queue: {
            body: { action: 'stop_queue' },
            description: 'Stop the queue processor'
          },
          clear_queue: {
            body: { action: 'clear_queue' },
            description: 'Clear all jobs from the queue (emergency action)'
          }
        }
      }
    });

  } catch (error) {
    console.error('Error getting trigger API status:', error);
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
