'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { GenerationJob, GenerationProgress } from '~/types/push-server.types';
import type { Database } from '~/lib/database.types';

const server = getSupabaseServerClient();

/**
 * Service for managing content generation status and progress tracking
 */
export class ContentGenerationStatusService {
  
  /**
   * Create a new content generation job
   */
  async createGenerationJob(job: Omit<GenerationJob, 'id' | 'created_at' | 'progress'>): Promise<GenerationJob> {
    const jobData = {
      task_id: job.task_id,
      company_id: job.company_id,
      campaign_id: job.campaign_id,
      priority: job.priority,
      status: job.status,
      retry_count: job.retry_count,
      max_retries: job.max_retries,
      error_message: job.error?.message,
      error_details: job.error ? JSON.stringify(job.error) : null,
      result_data: job.result ? JSON.stringify(job.result) : null,
      processing_time_ms: null,
      metadata: JSON.stringify({})
    };

    const { data, error } = await server
      .from('content_generation_jobs')
      .insert(jobData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create generation job: ${error.message}`);
    }

    // Create initial steps
    await this.createGenerationSteps(data.id);

    return this.mapDatabaseJobToGenerationJob(data);
  }

  /**
   * Update a generation job
   */
  async updateGenerationJob(
    jobId: string, 
    updates: Partial<Pick<GenerationJob, 'status' | 'started_at' | 'completed_at' | 'error' | 'result' | 'processing_time_ms'>>
  ): Promise<void> {
    const updateData: any = {};

    if (updates.status) updateData.status = updates.status;
    if (updates.started_at) updateData.started_at = updates.started_at;
    if (updates.completed_at) updateData.completed_at = updates.completed_at;
    if (updates.processing_time_ms) updateData.processing_time_ms = updates.processing_time_ms;
    if (updates.error) {
      updateData.error_message = updates.error.message;
      updateData.error_details = JSON.stringify(updates.error);
    }
    if (updates.result) {
      updateData.result_data = JSON.stringify(updates.result);
    }

    const { error } = await server
      .from('content_generation_jobs')
      .update(updateData)
      .eq('id', jobId);

    if (error) {
      throw new Error(`Failed to update generation job: ${error.message}`);
    }
  }

  /**
   * Get a generation job by ID
   */
  async getGenerationJob(jobId: string): Promise<GenerationJob | null> {
    const { data, error } = await server
      .from('content_generation_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to get generation job: ${error.message}`);
    }

    return this.mapDatabaseJobToGenerationJob(data);
  }

  /**
   * Get generation job by task ID
   */
  async getGenerationJobByTaskId(taskId: string): Promise<GenerationJob | null> {
    const { data, error } = await server
      .from('content_generation_jobs')
      .select('*')
      .eq('task_id', taskId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to get generation job by task ID: ${error.message}`);
    }

    return this.mapDatabaseJobToGenerationJob(data);
  }

  /**
   * Get all generation jobs for a campaign
   */
  async getGenerationJobsForCampaign(campaignId: string): Promise<GenerationJob[]> {
    const { data, error } = await server
      .from('content_generation_jobs')
      .select('*')
      .eq('campaign_id', campaignId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to get generation jobs for campaign: ${error.message}`);
    }

    return data.map(job => this.mapDatabaseJobToGenerationJob(job));
  }

  /**
   * Create initial generation steps for a job
   */
  private async createGenerationSteps(jobId: string): Promise<void> {
    const steps = [
      { job_id: jobId, step_name: 'content', status: 'pending' },
      { job_id: jobId, step_name: 'visual_description', status: 'pending' },
      { job_id: jobId, step_name: 'image', status: 'pending' },
      { job_id: jobId, step_name: 'assembly', status: 'pending' }
    ];

    const { error } = await server
      .from('content_generation_steps')
      .insert(steps);

    if (error) {
      throw new Error(`Failed to create generation steps: ${error.message}`);
    }
  }

  /**
   * Update a generation step
   */
  async updateGenerationStep(
    jobId: string,
    stepName: string,
    updates: {
      status?: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
      started_at?: string;
      completed_at?: string;
      error_message?: string;
      error_details?: any;
      request_data?: any;
      response_data?: any;
      processing_time_ms?: number;
      retry_count?: number;
    }
  ): Promise<void> {
    const updateData: any = { ...updates };
    
    if (updates.error_details) {
      updateData.error_details = JSON.stringify(updates.error_details);
    }
    if (updates.request_data) {
      updateData.request_data = JSON.stringify(updates.request_data);
    }
    if (updates.response_data) {
      updateData.response_data = JSON.stringify(updates.response_data);
    }

    const { error } = await server
      .from('content_generation_steps')
      .update(updateData)
      .eq('job_id', jobId)
      .eq('step_name', stepName);

    if (error) {
      throw new Error(`Failed to update generation step: ${error.message}`);
    }
  }

  /**
   * Get generation progress for a job
   */
  async getGenerationProgress(jobId: string): Promise<GenerationProgress | null> {
    // Use the database function for efficient progress calculation
    const { data, error } = await server.rpc('get_generation_progress', {
      job_id_param: jobId
    });

    if (error) {
      throw new Error(`Failed to get generation progress: ${error.message}`);
    }

    if (!data || data.error) {
      return null;
    }

    return {
      task_id: data.task_id,
      current_step: this.getCurrentStep(data.steps),
      completed_steps: data.steps
        .filter((step: any) => step.status === 'completed')
        .map((step: any) => step.step_name),
      failed_steps: data.steps
        .filter((step: any) => step.status === 'failed')
        .map((step: any) => step.step_name),
      total_steps: 4, // content, visual_description, image, assembly
      progress_percentage: data.progress_percentage,
      estimated_completion_time: this.estimateCompletionTime(data.steps, data.progress_percentage),
      error: data.error_message ? {
        code: 'GENERATION_ERROR',
        message: data.error_message
      } : undefined
    };
  }

  /**
   * Get jobs that are ready to be processed from the queue
   */
  async getQueuedJobs(limit: number = 10): Promise<GenerationJob[]> {
    const { data, error } = await server
      .from('content_generation_jobs')
      .select('*')
      .eq('status', 'queued')
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get queued jobs: ${error.message}`);
    }

    return data.map(job => this.mapDatabaseJobToGenerationJob(job));
  }

  /**
   * Add job to processing queue
   */
  async addToQueue(jobId: string, priority: number = 0): Promise<void> {
    const { error } = await server
      .from('content_generation_queue')
      .insert({
        job_id: jobId,
        priority,
        scheduled_at: new Date().toISOString(),
        attempts: 0,
        max_attempts: 3
      });

    if (error) {
      throw new Error(`Failed to add job to queue: ${error.message}`);
    }
  }

  /**
   * Remove job from queue
   */
  async removeFromQueue(jobId: string): Promise<void> {
    const { error } = await server
      .from('content_generation_queue')
      .delete()
      .eq('job_id', jobId);

    if (error) {
      throw new Error(`Failed to remove job from queue: ${error.message}`);
    }
  }

  /**
   * Map database job record to GenerationJob type
   */
  private mapDatabaseJobToGenerationJob(data: any): GenerationJob {
    return {
      id: data.id,
      task_id: data.task_id,
      company_id: data.company_id,
      campaign_id: data.campaign_id,
      priority: data.priority,
      status: data.status,
      created_at: data.created_at,
      started_at: data.started_at,
      completed_at: data.completed_at,
      progress: {
        task_id: data.task_id,
        current_step: 'content', // Will be updated when fetching progress
        completed_steps: [],
        failed_steps: [],
        total_steps: 4,
        progress_percentage: 0
      },
      result: data.result_data ? JSON.parse(data.result_data) : undefined,
      error: data.error_message ? {
        code: 'GENERATION_ERROR',
        message: data.error_message,
        details: data.error_details ? JSON.parse(data.error_details) : undefined
      } : undefined,
      retry_count: data.retry_count,
      max_retries: data.max_retries
    };
  }

  /**
   * Determine current step from steps array
   */
  private getCurrentStep(steps: any[]): 'content' | 'visual_description' | 'image' | 'assembly' {
    const stepOrder = ['content', 'visual_description', 'image', 'assembly'];
    
    for (const stepName of stepOrder) {
      const step = steps.find(s => s.step_name === stepName);
      if (!step || step.status === 'pending' || step.status === 'processing') {
        return stepName as any;
      }
      if (step.status === 'failed') {
        return stepName as any;
      }
    }
    
    return 'assembly'; // All steps completed
  }

  /**
   * Estimate completion time based on current progress
   */
  private estimateCompletionTime(steps: any[], progressPercentage: number): string | undefined {
    if (progressPercentage === 0) return undefined;
    
    const completedSteps = steps.filter(s => s.status === 'completed');
    if (completedSteps.length === 0) return undefined;
    
    const avgTimePerStep = completedSteps.reduce((sum, step) => {
      return sum + (step.processing_time_ms || 30000); // Default 30s per step
    }, 0) / completedSteps.length;
    
    const remainingSteps = 4 - completedSteps.length;
    const estimatedRemainingTime = remainingSteps * avgTimePerStep;
    
    const completionTime = new Date(Date.now() + estimatedRemainingTime);
    return completionTime.toISOString();
  }
}

// Export singleton instance
export const contentGenerationStatusService = new ContentGenerationStatusService();
