'use server';

import axios, { AxiosResponse, AxiosError } from 'axios';
import {
  GenerateContentRequest,
  GenerateContentResponse,
  GenerateVisualDescriptionRequest,
  GenerateVisualDescriptionResponse,
  GenerateImageRequest,
  GenerateImageResponse,
  PushServerResponse,
  PushServerError,
  RateLimitInfo,
  ApiCallConfig,
} from '~/types/push-server.types';

/**
 * Push Server API Integration Service
 * Handles all communication with Push Server endpoints for content generation
 */
export class PushServerApiService {
  private baseUrl: string;
  private defaultConfig: ApiCallConfig;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || '';
    this.defaultConfig = {
      timeout_ms: 30000, // 30 seconds
      max_retries: 3,
      retry_delay_ms: 1000,
      exponential_backoff: true,
    };

    if (!this.baseUrl) {
      throw new Error('NEXT_PUBLIC_PUSH_SERVER environment variable is not set');
    }
  }

  /**
   * Generate content for a task using Push Server
   */
  async generateContent(
    request: GenerateContentRequest,
    config?: Partial<ApiCallConfig>
  ): Promise<GenerateContentResponse> {
    const endpoint = '/generate-content';
    const finalConfig = { ...this.defaultConfig, ...config };

    try {
      const response = await this.makeApiCall<GenerateContentResponse>(
        'POST',
        endpoint,
        request,
        finalConfig
      );

      return response.result || response;
    } catch (error) {
      throw this.handleApiError(error, 'Content generation failed');
    }
  }

  /**
   * Generate visual description using existing endpoint
   */
  async generateVisualDescription(
    request: GenerateVisualDescriptionRequest,
    config?: Partial<ApiCallConfig>
  ): Promise<GenerateVisualDescriptionResponse> {
    const endpoint = '/generate-visual-description';
    const finalConfig = { ...this.defaultConfig, ...config };

    try {
      // Transform request to match existing API format
      const apiRequest = {
        brand_brief: request.brand_brief,
        content: request.content,
        initial_visual_desc: request.initial_visual_desc || 'NOT_PROVIDED',
        image_gen_styles: request.image_gen_styles,
      };

      const response = await this.makeApiCall<string>(
        'POST',
        endpoint,
        apiRequest,
        finalConfig
      );

      // Parse the response - existing endpoint returns a string
      const visualDescription = typeof response === 'string' ? response : response.result;

      // Create structured response
      return {
        visual_description: visualDescription,
        style_elements: {
          color_palette: [],
          mood: 'professional',
          composition: 'balanced',
          lighting: 'natural',
        },
        technical_specs: {
          aspect_ratio: '16:9',
          resolution: '1920x1080',
          format: 'PNG',
        },
      };
    } catch (error) {
      throw this.handleApiError(error, 'Visual description generation failed');
    }
  }

  /**
   * Generate image using Push Server
   */
  async generateImage(
    request: GenerateImageRequest,
    config?: Partial<ApiCallConfig>
  ): Promise<GenerateImageResponse> {
    const endpoint = '/generate-image';
    const finalConfig = { ...this.defaultConfig, ...config };

    try {
      const response = await this.makeApiCall<GenerateImageResponse>(
        'POST',
        endpoint,
        request,
        finalConfig
      );

      return response.result || response;
    } catch (error) {
      throw this.handleApiError(error, 'Image generation failed');
    }
  }

  /**
   * Make API call with retry logic and error handling
   */
  private async makeApiCall<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    config: ApiCallConfig = this.defaultConfig
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= config.max_retries; attempt++) {
      try {
        const response: AxiosResponse<T> = await axios({
          method,
          url,
          data,
          timeout: config.timeout_ms,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        // Check for rate limiting headers
        this.handleRateLimitHeaders(response.headers);

        return response.data;
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on certain errors
        if (this.shouldNotRetry(error as AxiosError)) {
          break;
        }

        // Don't retry on last attempt
        if (attempt === config.max_retries) {
          break;
        }

        // Calculate delay for next attempt
        const delay = config.exponential_backoff
          ? config.retry_delay_ms * Math.pow(2, attempt)
          : config.retry_delay_ms;

        console.warn(`API call failed (attempt ${attempt + 1}/${config.max_retries + 1}), retrying in ${delay}ms:`, error);
        
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * Handle API errors and convert to PushServerError
   */
  private handleApiError(error: any, context: string): PushServerError {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      
      return {
        code: axiosError.code || 'UNKNOWN_ERROR',
        message: `${context}: ${axiosError.message}`,
        details: {
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data,
        },
        retry_after: this.extractRetryAfter(axiosError.response?.headers),
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: `${context}: ${error.message || 'Unknown error occurred'}`,
      details: error,
    };
  }

  /**
   * Check if error should not be retried
   */
  private shouldNotRetry(error: AxiosError): boolean {
    if (!error.response) {
      return false; // Network errors should be retried
    }

    const status = error.response.status;
    
    // Don't retry client errors (4xx) except rate limiting
    if (status >= 400 && status < 500 && status !== 429) {
      return true;
    }

    return false;
  }

  /**
   * Handle rate limiting headers
   */
  private handleRateLimitHeaders(headers: any): RateLimitInfo | null {
    const limit = headers['x-ratelimit-limit'];
    const remaining = headers['x-ratelimit-remaining'];
    const reset = headers['x-ratelimit-reset'];
    const retryAfter = headers['retry-after'];

    if (limit || remaining || reset) {
      const rateLimitInfo: RateLimitInfo = {
        limit: parseInt(limit) || 0,
        remaining: parseInt(remaining) || 0,
        reset_time: parseInt(reset) || 0,
        retry_after: retryAfter ? parseInt(retryAfter) : undefined,
      };

      // Log rate limit warnings
      if (rateLimitInfo.remaining < 10) {
        console.warn('Push Server API rate limit warning:', rateLimitInfo);
      }

      return rateLimitInfo;
    }

    return null;
  }

  /**
   * Extract retry-after header value
   */
  private extractRetryAfter(headers?: any): number | undefined {
    if (!headers) return undefined;
    
    const retryAfter = headers['retry-after'];
    return retryAfter ? parseInt(retryAfter) : undefined;
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Health check for Push Server
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; latency_ms: number }> {
    const startTime = Date.now();
    
    try {
      await axios.get(`${this.baseUrl}/health`, { timeout: 5000 });
      return {
        status: 'healthy',
        latency_ms: Date.now() - startTime,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        latency_ms: Date.now() - startTime,
      };
    }
  }
}

// Export singleton instance
export const pushServerApi = new PushServerApiService();
