'use server';

import { 
  GenerationJob, 
  ContentPackage, 
  GenerationStep,
  PushServerError,
  GenerateContentRequest,
  GenerateVisualDescriptionRequest,
  GenerateImageRequest
} from '~/types/push-server.types';
import { pushServerApi } from './push-server-api';
import { contentGenerationStatusService } from './content-generation-status';
import { getCompanyContentById, updateCompanyContent } from './company-content';
import { CompanyContent } from '~/types/company-content';

/**
 * Content Generation Orchestration Service
 * Manages the complete content generation pipeline from task to finished content package
 */
export class ContentGenerationOrchestrator {
  
  /**
   * Process a single generation job through the complete pipeline
   */
  async processGenerationJob(jobId: string): Promise<ContentPackage> {
    const startTime = Date.now();
    
    try {
      // Get the job details
      const job = await contentGenerationStatusService.getGenerationJob(jobId);
      if (!job) {
        throw new Error(`Generation job ${jobId} not found`);
      }

      // Update job status to processing
      await contentGenerationStatusService.updateGenerationJob(jobId, {
        status: 'processing',
        started_at: new Date().toISOString()
      });

      console.log(`Starting content generation for job ${jobId}, task ${job.task_id}`);

      // Get the task content details
      const taskContent = await this.getTaskContent(job.task_id);
      if (!taskContent) {
        throw new Error(`Task content not found for task ID: ${job.task_id}`);
      }

      // Execute the generation pipeline
      const contentPackage = await this.executeGenerationPipeline(job, taskContent);

      // Update job as completed
      const processingTime = Date.now() - startTime;
      await contentGenerationStatusService.updateGenerationJob(jobId, {
        status: 'completed',
        completed_at: new Date().toISOString(),
        result: contentPackage,
        processing_time_ms: processingTime
      });

      // Update the original task content with generated data
      await this.updateTaskWithGeneratedContent(job.task_id, contentPackage);

      console.log(`Content generation completed for job ${jobId} in ${processingTime}ms`);
      return contentPackage;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const pushServerError: PushServerError = {
        code: 'ORCHESTRATION_ERROR',
        message: error instanceof Error ? error.message : 'Unknown orchestration error',
        details: error
      };

      // Update job as failed
      await contentGenerationStatusService.updateGenerationJob(jobId, {
        status: 'failed',
        completed_at: new Date().toISOString(),
        error: pushServerError,
        processing_time_ms: processingTime
      });

      console.error(`Content generation failed for job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Execute the complete generation pipeline
   */
  private async executeGenerationPipeline(
    job: GenerationJob, 
    taskContent: CompanyContent
  ): Promise<ContentPackage> {
    const pipeline = [
      { step: 'content' as GenerationStep, handler: this.generateContent.bind(this) },
      { step: 'visual_description' as GenerationStep, handler: this.generateVisualDescription.bind(this) },
      { step: 'image' as GenerationStep, handler: this.generateImage.bind(this) },
      { step: 'assembly' as GenerationStep, handler: this.assembleContentPackage.bind(this) }
    ];

    let contentPackage: Partial<ContentPackage> = {
      task_id: job.task_id,
      status: 'partial',
      created_at: new Date().toISOString(),
      processing_time_ms: 0
    };

    for (const { step, handler } of pipeline) {
      const stepStartTime = Date.now();
      
      try {
        // Update step status to processing
        await contentGenerationStatusService.updateGenerationStep(job.id, step, {
          status: 'processing',
          started_at: new Date().toISOString()
        });

        console.log(`Executing step ${step} for job ${job.id}`);

        // Execute the step
        contentPackage = await handler(job, taskContent, contentPackage);

        // Update step as completed
        const stepProcessingTime = Date.now() - stepStartTime;
        await contentGenerationStatusService.updateGenerationStep(job.id, step, {
          status: 'completed',
          completed_at: new Date().toISOString(),
          processing_time_ms: stepProcessingTime
        });

        console.log(`Step ${step} completed in ${stepProcessingTime}ms`);

      } catch (error) {
        const stepProcessingTime = Date.now() - stepStartTime;
        const errorMessage = error instanceof Error ? error.message : 'Unknown step error';
        
        // Update step as failed
        await contentGenerationStatusService.updateGenerationStep(job.id, step, {
          status: 'failed',
          completed_at: new Date().toISOString(),
          processing_time_ms: stepProcessingTime,
          error_message: errorMessage,
          error_details: error
        });

        console.error(`Step ${step} failed:`, error);

        // Decide whether to continue or fail the entire job
        if (this.isStepCritical(step)) {
          throw error;
        } else {
          // Mark step as skipped and continue
          await contentGenerationStatusService.updateGenerationStep(job.id, step, {
            status: 'skipped'
          });
          console.warn(`Step ${step} failed but is not critical, continuing...`);
        }
      }
    }

    return contentPackage as ContentPackage;
  }

  /**
   * Step 1: Generate content using Push Server
   */
  private async generateContent(
    job: GenerationJob,
    taskContent: CompanyContent,
    contentPackage: Partial<ContentPackage>
  ): Promise<Partial<ContentPackage>> {
    
    // Prepare content generation request
    const request: GenerateContentRequest = {
      task_id: job.task_id,
      task_title: taskContent.task_title || 'Untitled Task',
      task_description: taskContent.task_description || '',
      content_type: taskContent.content_type || 'social_post',
      channel: taskContent.channel || 'general',
      brand_brief: await this.getBrandBrief(job.company_id),
      campaign_brief: await this.getCampaignBrief(job.campaign_id),
      language: taskContent.language || 'en',
      company_id: job.company_id,
      campaign_id: job.campaign_id
    };

    // Log request data
    await contentGenerationStatusService.updateGenerationStep(job.id, 'content', {
      request_data: request
    });

    // Call Push Server API
    const response = await pushServerApi.generateContent(request);

    // Log response data
    await contentGenerationStatusService.updateGenerationStep(job.id, 'content', {
      response_data: response
    });

    return {
      ...contentPackage,
      content: response
    };
  }

  /**
   * Step 2: Generate visual description
   */
  private async generateVisualDescription(
    job: GenerationJob,
    taskContent: CompanyContent,
    contentPackage: Partial<ContentPackage>
  ): Promise<Partial<ContentPackage>> {
    
    if (!contentPackage.content) {
      throw new Error('Content must be generated before visual description');
    }

    // Prepare visual description request
    const request: GenerateVisualDescriptionRequest = {
      brand_brief: await this.getBrandBrief(job.company_id),
      content: contentPackage.content.content.body,
      initial_visual_desc: taskContent.visual_description || '',
      image_gen_styles: await this.getImageGenerationStyles(job.company_id),
      task_id: job.task_id,
      content_type: taskContent.content_type || 'social_post'
    };

    // Log request data
    await contentGenerationStatusService.updateGenerationStep(job.id, 'visual_description', {
      request_data: request
    });

    // Call Push Server API
    const response = await pushServerApi.generateVisualDescription(request);

    // Log response data
    await contentGenerationStatusService.updateGenerationStep(job.id, 'visual_description', {
      response_data: response
    });

    return {
      ...contentPackage,
      visual_description: response
    };
  }

  /**
   * Step 3: Generate image
   */
  private async generateImage(
    job: GenerationJob,
    taskContent: CompanyContent,
    contentPackage: Partial<ContentPackage>
  ): Promise<Partial<ContentPackage>> {
    
    if (!contentPackage.visual_description) {
      throw new Error('Visual description must be generated before image');
    }

    // Skip image generation if task doesn't require it
    if (!taskContent.has_image) {
      console.log(`Skipping image generation for task ${job.task_id} - no image required`);
      return contentPackage;
    }

    // Prepare image generation request
    const request: GenerateImageRequest = {
      image_prompt: contentPackage.visual_description.visual_description,
      aspect_ratio: contentPackage.visual_description.technical_specs.aspect_ratio,
      prompt_upsampling: 'false',
      language: taskContent.language || 'English',
      quality: 'hd'
    };

    // Log request data
    await contentGenerationStatusService.updateGenerationStep(job.id, 'image', {
      request_data: request
    });

    // Call Push Server API (using existing generate-image endpoint)
    const response = await pushServerApi.generateImage(request);

    // Log response data
    await contentGenerationStatusService.updateGenerationStep(job.id, 'image', {
      response_data: response
    });

    return {
      ...contentPackage,
      generated_image: response
    };
  }

  /**
   * Step 4: Assemble final content package
   */
  private async assembleContentPackage(
    job: GenerationJob,
    taskContent: CompanyContent,
    contentPackage: Partial<ContentPackage>
  ): Promise<Partial<ContentPackage>> {
    
    // Validate required components
    if (!contentPackage.content) {
      throw new Error('Content is required for package assembly');
    }

    if (!contentPackage.visual_description) {
      throw new Error('Visual description is required for package assembly');
    }

    // Determine final status
    const hasAllRequiredComponents = 
      contentPackage.content && 
      contentPackage.visual_description && 
      (!taskContent.has_image || contentPackage.generated_image);

    const finalPackage: ContentPackage = {
      task_id: job.task_id,
      content: contentPackage.content!,
      visual_description: contentPackage.visual_description!,
      generated_image: contentPackage.generated_image,
      status: hasAllRequiredComponents ? 'complete' : 'partial',
      created_at: contentPackage.created_at!,
      processing_time_ms: Date.now() - new Date(contentPackage.created_at!).getTime()
    };

    // Log assembly data
    await contentGenerationStatusService.updateGenerationStep(job.id, 'assembly', {
      response_data: finalPackage
    });

    return finalPackage;
  }

  /**
   * Get task content from database
   */
  private async getTaskContent(taskId: string): Promise<CompanyContent | null> {
    try {
      // First try to find by task_id field
      const { data, error } = await (await import('@kit/supabase/server-client')).getSupabaseServerClient()
        .from('company_content')
        .select('*')
        .eq('task_id', taskId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        return data as CompanyContent;
      }

      // If not found by task_id, try by id (in case taskId is actually the content ID)
      return await getCompanyContentById(taskId);
    } catch (error) {
      console.error(`Error getting task content for ${taskId}:`, error);
      return null;
    }
  }

  /**
   * Update task content with generated data
   */
  private async updateTaskWithGeneratedContent(
    taskId: string, 
    contentPackage: ContentPackage
  ): Promise<void> {
    try {
      const updates: Partial<CompanyContent> = {
        content: contentPackage.content.content.body,
        visual_description: contentPackage.visual_description.visual_description,
        generation_status: 'completed',
        generation_completed_at: new Date().toISOString()
      };

      // Add image data if available
      if (contentPackage.generated_image) {
        updates.image_url = contentPackage.generated_image.image_url;
        updates.image_path = contentPackage.generated_image.image_path;
      }

      // Find the content record to update
      const taskContent = await this.getTaskContent(taskId);
      if (taskContent?.id) {
        await updateCompanyContent(taskContent.id, updates);
        console.log(`Updated task content ${taskContent.id} with generated data`);
      } else {
        console.warn(`Could not find task content to update for task ID: ${taskId}`);
      }
    } catch (error) {
      console.error(`Error updating task content for ${taskId}:`, error);
      // Don't throw - this is not critical for the generation process
    }
  }

  /**
   * Get brand brief for content generation
   */
  private async getBrandBrief(companyId: string): Promise<string> {
    try {
      const { data, error } = await (await import('@kit/supabase/server-client')).getSupabaseServerClient()
        .from('company_brand')
        .select('*')
        .eq('company_id', companyId)
        .single();

      if (error || !data) {
        return 'No brand brief available';
      }

      // Construct brand brief from available data
      const brandBrief = {
        brand_name: data.brand_name || 'Unknown Brand',
        brand_description: data.brand_description || '',
        brand_voice: data.brand_voice || '',
        brand_personality: data.brand_personality || '',
        target_audience: data.target_audience || '',
        value_proposition: data.value_proposition || ''
      };

      return JSON.stringify(brandBrief);
    } catch (error) {
      console.error('Error getting brand brief:', error);
      return 'No brand brief available';
    }
  }

  /**
   * Get campaign brief for content generation
   */
  private async getCampaignBrief(campaignId: string): Promise<string> {
    try {
      const { data, error } = await (await import('@kit/supabase/server-client')).getSupabaseServerClient()
        .from('company_campaigns')
        .select('*')
        .eq('id', campaignId)
        .single();

      if (error || !data) {
        return 'No campaign brief available';
      }

      // Construct campaign brief from available data
      const campaignBrief = {
        name: data.name || 'Untitled Campaign',
        objectives: data.objectives || '',
        audience: data.audience || '',
        messaging: data.messaging || '',
        tone: data.tone || '',
        guidelines: data.guidelines || ''
      };

      return JSON.stringify(campaignBrief);
    } catch (error) {
      console.error('Error getting campaign brief:', error);
      return 'No campaign brief available';
    }
  }

  /**
   * Get image generation styles for the company
   */
  private async getImageGenerationStyles(companyId: string): Promise<string> {
    try {
      const { data, error } = await (await import('@kit/supabase/server-client')).getSupabaseServerClient()
        .from('company_brand')
        .select('visual_style, color_palette, image_style')
        .eq('company_id', companyId)
        .single();

      if (error || !data) {
        return JSON.stringify({ style: 'professional', colors: ['#000000', '#FFFFFF'] });
      }

      const styles = {
        visual_style: data.visual_style || 'professional',
        color_palette: data.color_palette || ['#000000', '#FFFFFF'],
        image_style: data.image_style || 'clean and modern'
      };

      return JSON.stringify(styles);
    } catch (error) {
      console.error('Error getting image generation styles:', error);
      return JSON.stringify({ style: 'professional', colors: ['#000000', '#FFFFFF'] });
    }
  }

  /**
   * Determine if a step is critical for the generation process
   */
  private isStepCritical(step: GenerationStep): boolean {
    // Content generation is always critical
    if (step === 'content') return true;
    
    // Visual description is critical for image generation
    if (step === 'visual_description') return true;
    
    // Image generation is optional (depends on task requirements)
    if (step === 'image') return false;
    
    // Assembly is critical
    if (step === 'assembly') return true;
    
    return true;
  }

  /**
   * Create and queue a new generation job for a task
   */
  async createAndQueueGenerationJob(
    taskId: string,
    companyId: string,
    campaignId: string,
    priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal'
  ): Promise<string> {
    
    // Check if job already exists for this task
    const existingJob = await contentGenerationStatusService.getGenerationJobByTaskId(taskId);
    if (existingJob && existingJob.status !== 'failed' && existingJob.status !== 'cancelled') {
      console.log(`Generation job already exists for task ${taskId}: ${existingJob.id}`);
      return existingJob.id;
    }

    // Create new generation job
    const job = await contentGenerationStatusService.createGenerationJob({
      task_id: taskId,
      company_id: companyId,
      campaign_id: campaignId,
      priority,
      status: 'queued',
      retry_count: 0,
      max_retries: 3
    });

    // Add to processing queue
    const priorityScore = this.getPriorityScore(priority);
    await contentGenerationStatusService.addToQueue(job.id, priorityScore);

    console.log(`Created and queued generation job ${job.id} for task ${taskId}`);
    return job.id;
  }

  /**
   * Get numeric priority score for queue ordering
   */
  private getPriorityScore(priority: 'low' | 'normal' | 'high' | 'urgent'): number {
    const scores = { low: 1, normal: 5, high: 10, urgent: 20 };
    return scores[priority];
  }
}

// Export singleton instance
export const contentGenerationOrchestrator = new ContentGenerationOrchestrator();
