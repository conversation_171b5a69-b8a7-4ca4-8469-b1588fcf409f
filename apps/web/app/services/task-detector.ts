'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { contentGenerationOrchestrator } from './content-generation-orchestrator';
import { CompanyContent } from '~/types/company-content';

/**
 * Task Detection Service
 * Monitors for new tasks that need content generation and automatically triggers the pipeline
 */
export class TaskDetectorService {
  
  /**
   * Scan for tasks that need content generation
   * This method can be called periodically or triggered by webhooks
   */
  async scanForTasksNeedingGeneration(): Promise<string[]> {
    const server = getSupabaseServerClient();
    const triggeredJobs: string[] = [];

    try {
      // Find tasks that are queued for generation but don't have active jobs
      const { data: queuedTasks, error } = await server
        .from('company_content')
        .select('*')
        .eq('generation_status', 'queued')
        .is('generation_job_id', null)
        .order('created_at', { ascending: true })
        .limit(50); // Process in batches

      if (error) {
        console.error('Error scanning for tasks needing generation:', error);
        return triggeredJobs;
      }

      if (!queuedTasks || queuedTasks.length === 0) {
        console.log('No tasks found needing generation');
        return triggeredJobs;
      }

      console.log(`Found ${queuedTasks.length} tasks needing generation`);

      // Process each task
      for (const task of queuedTasks) {
        try {
          const jobId = await this.triggerGenerationForTask(task as CompanyContent);
          if (jobId) {
            triggeredJobs.push(jobId);
            
            // Update the task to link it to the generation job
            await server
              .from('company_content')
              .update({
                generation_job_id: jobId,
                generation_status: 'generating',
                generation_started_at: new Date().toISOString()
              })
              .eq('id', task.id);

            console.log(`Triggered generation job ${jobId} for task ${task.id}`);
          }
        } catch (error) {
          console.error(`Failed to trigger generation for task ${task.id}:`, error);
          
          // Mark task as failed
          await server
            .from('company_content')
            .update({
              generation_status: 'failed',
              generation_error: error instanceof Error ? error.message : 'Unknown error'
            })
            .eq('id', task.id);
        }
      }

      console.log(`Successfully triggered ${triggeredJobs.length} generation jobs`);
      return triggeredJobs;

    } catch (error) {
      console.error('Error in scanForTasksNeedingGeneration:', error);
      return triggeredJobs;
    }
  }

  /**
   * Trigger content generation for a specific task
   */
  async triggerGenerationForTask(task: CompanyContent): Promise<string | null> {
    // Validate that task has required information for generation
    if (!this.isTaskReadyForGeneration(task)) {
      console.warn(`Task ${task.id} is not ready for generation - missing required fields`);
      return null;
    }

    // Determine priority based on task properties
    const priority = this.determinePriority(task);

    // Create and queue generation job
    const jobId = await contentGenerationOrchestrator.createAndQueueGenerationJob(
      task.task_id || task.id!, // Use task_id if available, otherwise use content id
      task.company_id!,
      task.campaign_id!,
      priority
    );

    return jobId;
  }

  /**
   * Check if a task is ready for content generation
   */
  private isTaskReadyForGeneration(task: CompanyContent): boolean {
    // Must have basic task information
    if (!task.task_title && !task.task_description) {
      return false;
    }

    // Must have company and campaign context
    if (!task.company_id || !task.campaign_id) {
      return false;
    }

    // Must not already have generated content (unless explicitly regenerating)
    if (task.content && task.content.trim() !== '' && task.generation_status !== 'queued') {
      return false;
    }

    return true;
  }

  /**
   * Determine priority for a task based on its properties
   */
  private determinePriority(task: CompanyContent): 'low' | 'normal' | 'high' | 'urgent' {
    // Check if task has a scheduled publishing time soon
    if (task.scheduled_publishing_time) {
      const scheduledTime = new Date(task.scheduled_publishing_time);
      const now = new Date();
      const hoursUntilPublish = (scheduledTime.getTime() - now.getTime()) / (1000 * 60 * 60);

      if (hoursUntilPublish < 2) return 'urgent';
      if (hoursUntilPublish < 24) return 'high';
      if (hoursUntilPublish < 72) return 'normal';
    }

    // Check content type priority
    if (task.content_type === 'urgent_announcement' || task.content_type === 'breaking_news') {
      return 'urgent';
    }

    if (task.content_type === 'campaign_launch' || task.content_type === 'product_launch') {
      return 'high';
    }

    // Default priority
    return 'normal';
  }

  /**
   * Process tasks that were created recently (for real-time triggering)
   */
  async processRecentTasks(minutesBack: number = 5): Promise<string[]> {
    const server = getSupabaseServerClient();
    const triggeredJobs: string[] = [];

    try {
      const cutoffTime = new Date(Date.now() - minutesBack * 60 * 1000).toISOString();

      const { data: recentTasks, error } = await server
        .from('company_content')
        .select('*')
        .gte('created_at', cutoffTime)
        .eq('generation_status', 'queued')
        .is('generation_job_id', null);

      if (error) {
        console.error('Error processing recent tasks:', error);
        return triggeredJobs;
      }

      if (!recentTasks || recentTasks.length === 0) {
        return triggeredJobs;
      }

      console.log(`Processing ${recentTasks.length} recent tasks for generation`);

      for (const task of recentTasks) {
        try {
          const jobId = await this.triggerGenerationForTask(task as CompanyContent);
          if (jobId) {
            triggeredJobs.push(jobId);
            
            // Update task with job reference
            await server
              .from('company_content')
              .update({
                generation_job_id: jobId,
                generation_status: 'generating',
                generation_started_at: new Date().toISOString()
              })
              .eq('id', task.id);
          }
        } catch (error) {
          console.error(`Failed to process recent task ${task.id}:`, error);
        }
      }

      return triggeredJobs;

    } catch (error) {
      console.error('Error in processRecentTasks:', error);
      return triggeredJobs;
    }
  }

  /**
   * Manually trigger generation for a specific task by ID
   */
  async manuallyTriggerTask(taskId: string): Promise<string> {
    const server = getSupabaseServerClient();

    try {
      // Get the task
      const { data: task, error } = await server
        .from('company_content')
        .select('*')
        .eq('id', taskId)
        .single();

      if (error || !task) {
        throw new Error(`Task ${taskId} not found`);
      }

      // Check if already has an active generation job
      if (task.generation_job_id && task.generation_status === 'generating') {
        throw new Error(`Task ${taskId} already has an active generation job`);
      }

      // Trigger generation
      const jobId = await this.triggerGenerationForTask(task as CompanyContent);
      if (!jobId) {
        throw new Error(`Failed to create generation job for task ${taskId}`);
      }

      // Update task status
      await server
        .from('company_content')
        .update({
          generation_job_id: jobId,
          generation_status: 'generating',
          generation_started_at: new Date().toISOString(),
          generation_error: null // Clear any previous errors
        })
        .eq('id', taskId);

      console.log(`Manually triggered generation job ${jobId} for task ${taskId}`);
      return jobId;

    } catch (error) {
      console.error(`Error manually triggering task ${taskId}:`, error);
      throw error;
    }
  }

  /**
   * Get statistics about tasks needing generation
   */
  async getGenerationStats(): Promise<{
    queued: number;
    generating: number;
    completed: number;
    failed: number;
    total: number;
  }> {
    const server = getSupabaseServerClient();

    try {
      const { data, error } = await server
        .from('company_content')
        .select('generation_status')
        .not('generation_status', 'is', null);

      if (error) {
        console.error('Error getting generation stats:', error);
        return { queued: 0, generating: 0, completed: 0, failed: 0, total: 0 };
      }

      const stats = {
        queued: 0,
        generating: 0,
        completed: 0,
        failed: 0,
        total: data?.length || 0
      };

      data?.forEach(item => {
        const status = item.generation_status;
        if (status === 'queued') stats.queued++;
        else if (status === 'generating') stats.generating++;
        else if (status === 'completed') stats.completed++;
        else if (status === 'failed') stats.failed++;
      });

      return stats;

    } catch (error) {
      console.error('Error in getGenerationStats:', error);
      return { queued: 0, generating: 0, completed: 0, failed: 0, total: 0 };
    }
  }

  /**
   * Clean up stale generation jobs (tasks stuck in 'generating' status)
   */
  async cleanupStaleJobs(hoursOld: number = 2): Promise<number> {
    const server = getSupabaseServerClient();
    let cleanedCount = 0;

    try {
      const cutoffTime = new Date(Date.now() - hoursOld * 60 * 60 * 1000).toISOString();

      // Find tasks that have been generating for too long
      const { data: staleTasks, error } = await server
        .from('company_content')
        .select('id, generation_job_id')
        .eq('generation_status', 'generating')
        .lt('generation_started_at', cutoffTime);

      if (error) {
        console.error('Error finding stale jobs:', error);
        return cleanedCount;
      }

      if (!staleTasks || staleTasks.length === 0) {
        return cleanedCount;
      }

      console.log(`Found ${staleTasks.length} stale generation jobs to clean up`);

      for (const task of staleTasks) {
        try {
          // Reset task status to queued so it can be retried
          await server
            .from('company_content')
            .update({
              generation_status: 'queued',
              generation_job_id: null,
              generation_started_at: null,
              generation_error: 'Previous generation job timed out and was reset'
            })
            .eq('id', task.id);

          cleanedCount++;
          console.log(`Reset stale task ${task.id} to queued status`);

        } catch (error) {
          console.error(`Error cleaning up stale task ${task.id}:`, error);
        }
      }

      console.log(`Cleaned up ${cleanedCount} stale generation jobs`);
      return cleanedCount;

    } catch (error) {
      console.error('Error in cleanupStaleJobs:', error);
      return cleanedCount;
    }
  }
}

// Export singleton instance
export const taskDetectorService = new TaskDetectorService();
