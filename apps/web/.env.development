# This file is used to define environment variables for the development environment.
# These values are only used when running the app in development mode.

# SUPABASE
# NEXT_PUBLIC_SUPABASE_URL=https://application.axcels.com
# NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
# NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRraGJnZmp5d3R5bXllaHVtZ2puIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzgwNTQxNzIsImV4cCI6MjA1MzYzMDE3Mn0.D2UXMqXY4rNuWtV9Hcl8_00qz-bjpyXY8vyqTqDzYCQ
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

## THIS IS FOR DEVELOPMENT ONLY - DO NOT USE IN PRODUCTION

SUPABASE_DB_WEBHOOK_SECRET=WEBHOOKSECRET
# EMAILS
EMAIL_SENDER="Makerkit <<EMAIL>>"
EMAIL_PORT=54325
EMAIL_HOST=localhost
EMAIL_TLS=false
EMAIL_USER=user
EMAIL_PASSWORD=password

# CONTACT FORM
CONTACT_EMAIL=<EMAIL>

# STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51Rqg90Bla1rVkwxZ1ju3gfU7yaUij9PAoulm6JQTNBpSHEMTz7FWL8nhjQZhbA6hz4yLeBsBlMlhPQBw6NSMuifO00TtU337Aq
STRIPE_SECRET_KEY=sk_test_51Rqg90Bla1rVkwxZPHnI3fy9mDHhN6pNhFXBSjZKaIi7UKhaFbbO94ZWtBYftrHMRaqPpjKozWslxf2zlGslTFm1003Kdae4wq
# Add stripe local listener webhook from the terminal (STRIPE CLI) in development mode
STRIPE_WEBHOOK_SECRET=


# MAILER
MAILER_PROVIDER=nodemailer
ZENROWS_API_KEY=****************************************
DOCUPANDA_API_KEY=0EI6iTOOz0cnPDHvCsOzVMO2HZG2
